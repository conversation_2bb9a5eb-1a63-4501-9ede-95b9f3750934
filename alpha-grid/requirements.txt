# Core Framework Dependencies
langgraph==0.0.48
duckdb==0.9.2
streamlit==1.28.0
fastapi==0.109.1
uvicorn==0.24.0

# HTTP and Async
httpx
aiohttp==3.9.0
requests==2.31.0
beautifulsoup4==4.12.2

# Data Processing
pandas==2.1.0
polars==0.19.0
numpy==1.24.0
multimethod==1.9.1

# Data Validation
pydantic>=2.9.0,<3.0.0
pydantic-settings==2.0.3
pandera==0.17.0

# Reliability and Retry Logic
tenacity==8.2.0
pybreaker==0.8.0

# Configuration and Security
python-dotenv==1.0.0
cryptography==41.0.0
pyyaml==6.0.1

# Logging and Monitoring
structlog==23.1.0
prometheus-client==0.19.0
psutil==5.9.6

# API Wrappers and Utilities
pycoingecko==3.1.0
web3>=6.0.0,<7.0.0
eth-typing>=3.0.0,<4.0.0
python-binance==1.0.19

# Rate Limiting
ratelimiter==1.2.0
asyncio-throttle==1.0.2

# Testing
pytest==7.4.0
pytest-asyncio==0.21.0
pytest-cov==4.1.0
hypothesis==6.88.0

# Development Tools
black==23.9.0
flake8==6.1.0
mypy==1.6.0

# Scientific Computing
scipy==1.11.0
scikit-learn==1.3.0
numba==0.58.0

# Visualization
plotly==5.17.0
altair==5.1.0

# Additional Utilities
rich==13.6.0
click==8.1.0
python-jose==3.3.0

# AI/LLM Integration
ollama==0.4.4
