# Alpha Grid

Alpha Grid is a self-contained, agentic application designed to ingest data from nine diverse sources, analyze and reason using multiple AI agents that collaborate to produce daily rankings and new coin picks.

## Quick Start

1.  **Install Dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

2.  **Set up Environment Variables:**
    Create a `.env` file in the `alpha-grid` directory and add the following:
    ```
    # Add any necessary API keys here
    ```

3.  **Initialize the Database:**
    ```bash
    python -c "import asyncio; from core.database import db_manager; asyncio.run(db_manager.initialize())"
    ```

4.  **Run the Application:**
    ```bash
    streamlit run alpha_grid_dashboard.py
    ```

## System Architecture

The system follows a modular, fault-tolerant pipeline built with LangGraph for agent orchestration, DuckDB for data storage, and Streamlit/FastAPI for outputs. Here’s the flow:

[9 Scraper Agents] → [Validator] → [Feature Engine] → [Jury (4 Agents)] → [Reporter]

*   **Scraper Agents:** Fetch raw data from external APIs/websites.
*   **Validator:** Ensures data integrity with schema and statistical checks.
*   **Feature Engine:** Processes data into normalized features (e.g., z-scores, EMAs).
*   **Jury:** Four specialist agents debate and vote on rankings.
*   **Reporter:** Presents results via a local UI and API.

## Troubleshooting

*   **Database Errors:** If you encounter database errors, try deleting the `alpha_grid.db` file in the `alpha-grid/data` directory and re-initializing the database.
*   **Dependency Issues:** If you have problems with dependencies, try creating a new virtual environment and reinstalling the requirements.